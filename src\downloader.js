const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const chalk = require('chalk');

class VideoDownloader {
  constructor() {
    this.currentProcess = null;
    this.isPaused = false;
    this.isCancelled = false;
  }

  async download(options, callbacks = {}) {
    const { onProgress, onLog } = callbacks;
    
    try {
      // Validate URL
      if (!options.url || !options.url.trim()) {
        throw new Error('URL is required');
      }

      // Ensure output directory exists
      await fs.ensureDir(options.output);

      onLog && onLog('🔍 Checking dependencies...', 'info');
      
      // Check yt-dlp availability
      const ytDlpAvailable = await this.checkYtDlp();
      if (!ytDlpAvailable) {
        throw new Error('yt-dlp not found. Please install it first.');
      }

      onLog && onLog('✅ Dependencies check passed', 'success');
      onLog && onLog('🎬 Starting video download...', 'info');

      // Build yt-dlp arguments
      const args = this.buildArguments(options);
      
      onLog && onLog(`📝 Command: yt-dlp ${args.join(' ')}`, 'info');

      // Check if quality exists (for video downloads)
      if (!options.audioOnly && options.quality !== 'best') {
        onLog && onLog('🔍 Checking if selected quality is available...', 'info');
        const qualityExists = await this.checkQualityExists(options.url, options.quality);

        if (!qualityExists) {
          onLog && onLog('⚠️  Selected quality not available, falling back to best quality', 'warning');
          // Rebuild args with best quality
          options.quality = 'best';
          // Remove the old format argument
          const formatIndex = args.findIndex(arg => arg === '--format');
          if (formatIndex !== -1) {
            args.splice(formatIndex, 2); // Remove --format and its value
          }
          // Add new quality arguments
          args.splice(-1, 0, ...this.getQualityArguments(options)); // Insert before URL
        }
      }

      // Start download process
      return new Promise((resolve, reject) => {
        // Try yt-dlp directly first, fallback to python -m yt_dlp
        let command = 'yt-dlp';
        let finalArgs = args;

        this.currentProcess = spawn(command, finalArgs, {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        this.isCancelled = false;
        this.isPaused = false;
        let errorOutput = '';

        // Attach event handlers
        this.attachEventHandlers(this.currentProcess, onProgress, onLog, errorOutput, resolve, reject, command, args);
      });

    } catch (error) {
      throw new Error(`Download error: ${error.message}`);
    }
  }

  attachEventHandlers(process, onProgress, onLog, errorOutput, resolve, reject, command, args) {
    // Handle stdout (progress and info)
    process.stdout.on('data', (data) => {
      const output = data.toString();
      if (!this.isCancelled && !this.isDebugMessage(output)) {
        this.processOutput(output, onProgress, onLog);
      }
    });

    // Handle stderr (errors and warnings)
    process.stderr.on('data', (data) => {
      const output = data.toString();
      errorOutput += output;
      if (!this.isCancelled) {
        // Always log stderr for debugging
        onLog && onLog(`[STDERR] ${output.trim()}`, 'warning');
        if (!this.isDebugMessage(output)) {
          this.processOutput(output, onProgress, onLog);
        }
      }
    });

    // Handle process completion
    process.on('close', (code) => {
      this.currentProcess = null;

      if (code === 0) {
        onLog && onLog('✅ Download completed successfully!', 'success');
        resolve();
      } else if (this.isCancelled) {
        onLog && onLog('⚠️  Download cancelled by user', 'warning');
        resolve();
      } else {
        // Provide detailed error information
        let errorMessage = `Download failed with exit code ${code}`;
        if (errorOutput.trim()) {
          errorMessage += `\n\nError details:\n${errorOutput.trim()}`;
        }

        // Common error patterns and solutions
        if (errorOutput.includes('HTTP Error 403') || errorOutput.includes('Forbidden')) {
          errorMessage += '\n\n💡 Possible solutions:\n- Video may be private or restricted\n- Try updating yt-dlp: pip install --upgrade yt-dlp';
        } else if (errorOutput.includes('HTTP Error 404') || errorOutput.includes('Not Found')) {
          errorMessage += '\n\n💡 Possible solutions:\n- Check if the URL is correct\n- Video may have been deleted';
        } else if (errorOutput.includes('Sign in to confirm your age')) {
          errorMessage += '\n\n💡 Solution:\n- This video has age restrictions and requires authentication';
        } else if (errorOutput.includes('Video unavailable')) {
          errorMessage += '\n\n💡 Possible solutions:\n- Video may be private, deleted, or region-blocked\n- Check the URL and try again';
        }

        reject(new Error(errorMessage));
      }
    });

    // Handle process errors
    process.on('error', (error) => {
      this.currentProcess = null;

      // If yt-dlp command failed, try python -m yt_dlp
      if (command === 'yt-dlp' && error.code === 'ENOENT') {
        onLog && onLog('⚠️  yt-dlp command not found, trying python -m yt_dlp...', 'warning');

        // Retry with python -m yt_dlp
        const newCommand = 'python';
        const newArgs = ['-m', 'yt_dlp', ...args];

        this.currentProcess = spawn(newCommand, newArgs, {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        // Re-attach all the event handlers for the new process
        this.attachEventHandlers(this.currentProcess, onProgress, onLog, errorOutput, resolve, reject, newCommand, newArgs);
        return;
      }

      reject(new Error(`Failed to start download: ${error.message}`));
    });
  }

  buildArguments(options) {
    const args = [
      '--no-playlist',
      '--embed-thumbnail',
      '--verbose'
    ];

    // Add quality/format arguments
    args.push(...this.getQualityArguments(options));

    // Add subtitle arguments
    if (options.subtitles && !options.audioOnly) {
      args.push('--embed-subs', '--write-auto-sub');
    }

    // Add output template
    const outputTemplate = path.join(options.output, '%(title)s.%(ext)s');
    args.push('-o', outputTemplate);

    // Add URL (must be last)
    args.push(options.url);

    return args;
  }

  getQualityArguments(options) {
    if (options.audioOnly) {
      return [
        '--extract-audio',
        '--audio-format', 'mp3',
        '--audio-quality', '0'
      ];
    }

    const qualityFormats = {
      'best': '(bestvideo[ext=mp4]+bestaudio[ext=m4a]/bestvideo+bestaudio)/best[ext=mp4]/best',
      '2160p': '(bestvideo[height=2160][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=2160]+bestaudio)',
      '1440p': '(bestvideo[height=1440][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=1440]+bestaudio)',
      '1080p': '(bestvideo[height=1080][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=1080]+bestaudio)',
      '720p': '(bestvideo[height=720][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=720]+bestaudio)',
      '480p': '(bestvideo[height=480][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=480]+bestaudio)',
      '360p': '(bestvideo[height=360][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=360]+bestaudio)'
    };

    const format = qualityFormats[options.quality] || qualityFormats['best'];
    return ['--format', format];
  }

  async checkYtDlp() {
    try {
      // Try yt-dlp directly first
      const process = spawn('yt-dlp', ['--version'], { stdio: 'pipe' });
      const result = await new Promise((resolve) => {
        process.on('close', (code) => resolve(code === 0));
        process.on('error', () => resolve(false));
      });

      if (result) return true;

      // Try python -m yt_dlp as fallback
      const pythonProcess = spawn('python', ['-m', 'yt_dlp', '--version'], { stdio: 'pipe' });
      return new Promise((resolve) => {
        pythonProcess.on('close', (code) => resolve(code === 0));
        pythonProcess.on('error', () => resolve(false));
      });
    } catch {
      return false;
    }
  }

  async checkQualityExists(url, quality) {
    try {
      const process = spawn('yt-dlp', ['--list-formats', '--no-playlist', url], {
        stdio: 'pipe'
      });

      return new Promise((resolve) => {
        let output = '';
        
        process.stdout.on('data', (data) => {
          output += data.toString();
        });

        process.on('close', (code) => {
          if (code !== 0) {
            resolve(true); // Assume quality exists if we can't check
            return;
          }

          const heightMap = {
            '2160p': '2160',
            '1440p': '1440', 
            '1080p': '1080',
            '720p': '720',
            '480p': '480',
            '360p': '360'
          };

          const targetHeight = heightMap[quality];
          if (!targetHeight) {
            resolve(true); // For 'best' or unknown qualities
            return;
          }

          const hasQuality = output.includes(`${targetHeight}p`) || output.includes(`${targetHeight}x`);
          resolve(hasQuality);
        });

        process.on('error', () => resolve(true));
      });
    } catch {
      return true; // Assume quality exists if check fails
    }
  }

  processOutput(output, onProgress, onLog) {
    // Handle errors
    if (output.toLowerCase().includes('error:')) {
      if (output.includes('Video unavailable') || output.includes('Private video')) {
        onLog && onLog('❌ Video is private or unavailable', 'error');
      } else if (output.includes('not found') || output.includes('404')) {
        onLog && onLog('❌ Video not found', 'error');
      } else if (output.includes('Sign in to confirm your age')) {
        onLog && onLog('❌ Age restriction - sign in required', 'error');
      } else if (output.includes('blocked')) {
        onLog && onLog('❌ Video blocked in your region', 'error');
      } else {
        onLog && onLog('⚠️  Download issue detected', 'warning');
      }
      return;
    }

    // Parse download progress
    const progressMatch = output.match(/(\d+\.?\d*)%.*?(\d+\.?\d*\w+\/s)/);
    if (progressMatch && onProgress) {
      const percentage = parseFloat(progressMatch[1]);
      const speed = progressMatch[2];
      
      onProgress({
        percentage: percentage,
        speed: speed,
        eta: this.extractETA(output)
      });
    }

    // Handle FFmpeg processing
    if (output.includes('[ffmpeg]')) {
      onProgress && onProgress({ percentage: 95, status: 'Processing video...' });
    }

    // Log other important messages
    if (output.includes('[download]') && !output.includes('%')) {
      const fileName = this.extractFileName(output);
      if (fileName) {
        onLog && onLog(`📁 Downloading: ${fileName}`, 'info');
      }
    }
  }

  extractETA(output) {
    const etaMatch = output.match(/ETA\s+(\d+:\d+)/);
    return etaMatch ? etaMatch[1] : null;
  }

  extractFileName(output) {
    const match = output.match(/Destination:\s*(.+)|(\[download\].*?)(?:\s|$)/);
    if (match) {
      return match[1] || match[2];
    }
    return null;
  }

  isDebugMessage(output) {
    const debugPatterns = [
      '[debug]',
      'WARNING:',
      'Deleting original file'
    ];
    
    return debugPatterns.some(pattern => output.includes(pattern));
  }

  cancel() {
    if (this.currentProcess && !this.currentProcess.killed) {
      this.isCancelled = true;
      this.currentProcess.kill('SIGTERM');
    }
  }

  pause() {
    if (this.currentProcess && !this.currentProcess.killed) {
      this.isPaused = true;
      this.currentProcess.kill('SIGSTOP');
    }
  }

  resume() {
    if (this.currentProcess && !this.currentProcess.killed && this.isPaused) {
      this.isPaused = false;
      this.currentProcess.kill('SIGCONT');
    }
  }
}

module.exports = VideoDownloader;
