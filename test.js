#!/usr/bin/env node

const VideoDownloader = require('./src/downloader');
const DependencyChecker = require('./src/dependencies');
const chalk = require('chalk');

async function test() {
  console.log(chalk.cyan.bold('🧪 Testing Video Downloader CLI\n'));

  // Test dependency checking
  console.log(chalk.yellow('1. Testing dependency checker...'));
  const depChecker = new DependencyChecker();
  
  try {
    const results = await depChecker.checkAll();
    console.log(chalk.green('✅ Dependency check completed'));
    
    results.forEach(result => {
      const status = result.installed ? 
        chalk.green('✅ Installed') : 
        chalk.red('❌ Missing');
      console.log(`   ${result.name}: ${status}`);
      if (result.version && result.installed) {
        console.log(chalk.gray(`      Version: ${result.version}`));
      }
    });
  } catch (error) {
    console.error(chalk.red('❌ Dependency check failed:'), error.message);
  }

  console.log('\n' + chalk.yellow('2. Testing video downloader...'));
  
  // Test downloader
  const downloader = new VideoDownloader();
  
  try {
    // Test yt-dlp availability
    const ytDlpAvailable = await downloader.checkYtDlp();
    console.log(`   yt-dlp available: ${ytDlpAvailable ? chalk.green('✅ Yes') : chalk.red('❌ No')}`);
    
    if (!ytDlpAvailable) {
      console.log(chalk.yellow('   ⚠️  yt-dlp not found. Please install it first:'));
      console.log(chalk.gray('      pip install yt-dlp'));
      return;
    }

    // Test with a simple YouTube video (this is a test video)
    const testUrl = 'https://youtu.be/cYzJVZv-EWc';
    console.log(chalk.cyan(`   Testing with URL: ${testUrl}`));
    
    // Test quality checking
    console.log(chalk.yellow('   Testing quality check...'));
    const qualityExists = await downloader.checkQualityExists(testUrl, '720p');
    console.log(`   720p quality available: ${qualityExists ? chalk.green('✅ Yes') : chalk.yellow('⚠️  No')}`);
    
    console.log(chalk.green('\n✅ All tests completed successfully!'));
    console.log(chalk.gray('\nTo run a real download, use:'));
    console.log(chalk.cyan('   node index.js --interactive'));
    
  } catch (error) {
    console.error(chalk.red('❌ Test failed:'), error.message);
  }
}

// Run tests
test().catch(console.error);
